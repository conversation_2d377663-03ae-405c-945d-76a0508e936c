// src/app/dashboard/_components/dashboard-content.tsx
'use client';

import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import AuthModal from '../../../components/auth/auth-modal';
import { useAuth } from '../../../components/auth/auth-provider';
import PageWrapper from '../../../components/common/page-wrapper';

const DashboardContent = () => {
  const { user, loading } = useAuth();
  const router = useRouter();
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);

  // Handle successful login - redirect to dashboard
  const _handleAuthSuccess = () => {
    setIsAuthModalOpen(false);
    // The useEffect will handle the redirect once user state updates
  };

  useEffect(() => {
    // Set timeout to prevent infinite loading
    const timeoutId = setTimeout(() => {
      router.push('/product');
    }, 5000);

    // Only redirect if auth is fully loaded and user is definitely not authenticated
    if (!loading && (!user || !user.isAuthenticated)) {
      clearTimeout(timeoutId);
      router.push('/product');
      return;
    }

    // Clear timeout if user is authenticated
    if (!loading && user && user.isAuthenticated) {
      clearTimeout(timeoutId);
    }

    return () => clearTimeout(timeoutId);
  }, [user, loading, router]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-slate-600">Loading dashboard...</p>

          {/* Provide escape route */}
          <div className="mt-6">
            <button
              onClick={() => router.push('/product')}
              className="text-blue-600 hover:text-blue-700 underline text-sm"
            >
              Go to Product Page →
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!user || !user.isAuthenticated) {

    return (
      <>
        {/* Authentication Required Page - Product Theme */}
        <div className="min-h-screen flex flex-col bg-gradient-to-br from-indigo-50 via-white to-purple-50 relative overflow-hidden">
          {/* Background Elements */}
          <div className="absolute inset-0">
            <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
            <div className="absolute top-1/4 left-1/4 w-72 h-72 bg-gradient-to-r from-indigo-400 to-purple-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-float"></div>
            <div className="absolute top-3/4 right-1/4 w-96 h-96 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-float" style={{animationDelay: '2s'}}></div>
            <div className="absolute bottom-1/4 left-1/3 w-80 h-80 bg-gradient-to-r from-blue-400 to-indigo-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-float" style={{animationDelay: '4s'}}></div>
          </div>

          <div className="flex-grow flex items-center justify-center relative z-10 px-4">
            <div className="text-center max-w-2xl mx-auto">
              {/* Lock Icon */}
              <div className="inline-flex items-center justify-center w-24 h-24 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full mb-8 shadow-2xl shadow-indigo-500/25">
                <svg className="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-9a2 2 0 00-2-2H6a2 2 0 00-2 2v9a2 2 0 002 2zm10-12V9a4 4 0 00-8 0v2m8 0V9a4 4 0 00-4-4 4 4 0 00-4 4v2" />
                </svg>
              </div>

              {/* Heading */}
              <h1 className="text-4xl md:text-5xl font-bold mb-6">
                <span className="bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
                  Authentication Required
                </span>
              </h1>

              {/* Description */}
              <p className="text-xl text-slate-600 mb-8 leading-relaxed">
                Please sign in to access your EduPro dashboard and manage your school operations.
              </p>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
                <button
                  onClick={() => setIsAuthModalOpen(true)}
                  className="group relative px-8 py-4 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-2xl font-semibold text-lg shadow-2xl hover:shadow-indigo-500/25 transition-all duration-300 transform hover:-translate-y-1 hover:scale-105"
                >
                  <span className="relative z-10 flex items-center justify-center">
                    Sign In to Dashboard
                    <svg className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                    </svg>
                  </span>
                  <div className="absolute inset-0 bg-gradient-to-r from-indigo-700 to-purple-700 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </button>

                <button
                  onClick={() => router.push('/product')}
                  className="group px-8 py-4 border-2 border-slate-300 text-slate-700 rounded-2xl font-semibold text-lg hover:border-indigo-500 hover:text-indigo-600 hover:bg-indigo-50 transition-all duration-300 transform hover:-translate-y-1 hover:scale-105 bg-white/80 backdrop-blur-sm shadow-lg hover:shadow-xl"
                >
                  <span className="flex items-center justify-center">
                    Return Home
                    <svg className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                    </svg>
                  </span>
                </button>
              </div>

              {/* Trust Indicators */}
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 max-w-2xl mx-auto">
                <div className="flex items-center justify-center p-4 bg-white/60 backdrop-blur-sm rounded-2xl shadow-lg">
                  <svg className="w-8 h-8 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <div className="text-left">
                    <div className="font-semibold text-slate-800 text-sm">Secure Access</div>
                    <div className="text-xs text-slate-600">Enterprise security</div>
                  </div>
                </div>

                <div className="flex items-center justify-center p-4 bg-white/60 backdrop-blur-sm rounded-2xl shadow-lg">
                  <svg className="w-8 h-8 text-blue-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  <div className="text-left">
                    <div className="font-semibold text-slate-800 text-sm">FERPA Compliant</div>
                    <div className="text-xs text-slate-600">Data protection</div>
                  </div>
                </div>

                <div className="flex items-center justify-center p-4 bg-white/60 backdrop-blur-sm rounded-2xl shadow-lg">
                  <svg className="w-8 h-8 text-purple-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <div className="text-left">
                    <div className="font-semibold text-slate-800 text-sm">24/7 Support</div>
                    <div className="text-xs text-slate-600">Expert assistance</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Auth Modal */}
        <AuthModal
          isOpen={isAuthModalOpen}
          onClose={() => setIsAuthModalOpen(false)}
          defaultTab="login"
        />
      </>
    );
  }

  return (
    <PageWrapper>
      {/* Exciting News Banner */}
      <div className="bg-gradient-to-r from-indigo-600 to-blue-600 rounded-xl p-4 mb-4 text-white shadow-lg">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="bg-white/20 rounded-lg p-2">
              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div>
              <h3 className="text-base-app font-semibold">Version 2.0 is Live!</h3>
              <p className="text-blue-100 text-base-app opacity-90">
                Enhanced features, improved performance, and refreshed UI.
              </p>
            </div>
          </div>
          <button className="bg-white text-indigo-600 px-4 py-2 rounded-lg text-base font-semibold hover:bg-blue-50 transition-colors shadow-sm">
            Explore
          </button>
        </div>
      </div>

      {/* Quick Action Cards */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-2 mb-4">
        {/* Create Project */}
        <div className="bg-gradient-to-br from-blue-50 to-indigo-100 rounded-lg p-2 shadow-sm border border-blue-200/50 hover:shadow-md transition-all duration-300">
          <div className="w-6 h-6 bg-blue-500/20 rounded-lg flex items-center justify-center mb-1.5">
            <svg className="w-3 h-3 text-blue-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
          </div>
          <h3 className="font-semibold text-blue-900 mb-1 text-sm-app">Create Project</h3>
          <button className="text-blue-700 text-sm-app font-medium hover:text-blue-800 hover:underline transition-all">
            Start →
          </button>
        </div>

        {/* Upload Docs */}
        <div className="bg-gradient-to-br from-green-50 to-emerald-100 rounded-lg p-2 shadow-sm border border-green-200/50 hover:shadow-md transition-all duration-300">
          <div className="w-6 h-6 bg-green-500/20 rounded-lg flex items-center justify-center mb-1.5">
            <svg className="w-3 h-3 text-green-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
            </svg>
          </div>
          <h3 className="font-semibold text-green-900 mb-1 text-sm">Upload Docs</h3>
          <button className="text-green-700 text-sm font-medium hover:text-green-800 hover:underline transition-all">
            Upload →
          </button>
        </div>

        {/* View Analytics */}
        <div className="bg-gradient-to-br from-orange-50 to-amber-100 rounded-lg p-2 shadow-sm border border-orange-200/50 hover:shadow-md transition-all duration-300">
          <div className="w-6 h-6 bg-orange-500/20 rounded-lg flex items-center justify-center mb-1.5">
            <svg className="w-3 h-3 text-orange-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
          </div>
          <h3 className="font-semibold text-orange-900 mb-1 text-sm-app">Analytics</h3>
          <button className="text-orange-700 text-sm-app font-medium hover:text-orange-800 hover:underline transition-all">
            View →
          </button>
        </div>

        {/* Manage Users */}
        <div className="bg-gradient-to-br from-pink-50 to-rose-100 rounded-lg p-3 shadow-sm border border-pink-200/50 hover:shadow-md transition-all duration-300">
          <div className="w-8 h-8 bg-pink-500/20 rounded-lg flex items-center justify-center mb-2">
            <svg className="w-4 h-4 text-pink-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
            </svg>
          </div>
          <h3 className="font-semibold text-pink-900 mb-1 text-sm-app">Users</h3>
          <button className="text-pink-700 text-sm-app font-medium hover:text-pink-800 hover:underline transition-all">
            Manage →
          </button>
        </div>

        {/* Reports */}
        <div className="bg-gradient-to-br from-purple-50 to-violet-100 rounded-lg p-3 shadow-sm border border-purple-200/50 hover:shadow-md transition-all duration-300">
          <div className="w-8 h-8 bg-purple-500/20 rounded-lg flex items-center justify-center mb-2">
            <svg className="w-4 h-4 text-purple-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <h3 className="font-semibold text-purple-900 mb-1 text-sm-app">Reports</h3>
          <button className="text-purple-700 text-sm-app font-medium hover:text-purple-800 hover:underline transition-all">
            Generate →
          </button>
        </div>

        {/* Settings */}
        <div className="bg-gradient-to-br from-gray-50 to-slate-100 rounded-lg p-3 shadow-sm border border-gray-200/50 hover:shadow-md transition-all duration-300">
          <div className="w-8 h-8 bg-gray-500/20 rounded-lg flex items-center justify-center mb-2">
            <svg className="w-4 h-4 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
          </div>
          <h3 className="font-semibold text-gray-900 mb-1 text-sm">Settings</h3>
          <button className="text-gray-700 text-sm font-medium hover:text-gray-800 hover:underline transition-all">
            Configure →
          </button>
        </div>
      </div>

      {/* Performance Overview */}
      <div className="mb-6">
        <h2 className="section-header-md">Performance Overview</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* User Engagement */}
          <div className="card-lg hover:shadow-xl transition-all duration-200">
            <div className="flex items-center justify-between mb-4">
              <h3 className="card-title-sm">User Engagement</h3>
              <div className="text-blue-600 bg-blue-50 p-2 rounded-lg">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                </svg>
              </div>
            </div>
            <div className="text-3xl-app font-bold text-gray-900 mb-2">+15%</div>
            <div className="text-sm-app text-gray-600">Last 30 Days</div>
          </div>

          {/* Product Sales */}
          <div className="card-lg hover:shadow-xl transition-all duration-200">
            <div className="flex items-center justify-between mb-4">
              <h3 className="card-title-sm">Product Sales</h3>
              <div className="text-green-600 bg-green-50 p-2 rounded-lg">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                </svg>
              </div>
            </div>
            <div className="text-3xl-app font-bold text-gray-900 mb-2">+8%</div>
            <div className="text-sm-app text-gray-600">Last Quarter</div>
          </div>

          {/* Server Uptime */}
          <div className="bg-white rounded-xl p-5 shadow-lg border border-gray-200/50 hover:shadow-xl transition-all duration-200">
            <div className="flex items-center justify-between mb-4">
              <h3 className="font-semibold text-gray-900 text-base">Server Uptime</h3>
              <div className="text-purple-600 bg-purple-50 p-2 rounded-lg">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2" />
                </svg>
              </div>
            </div>
            <div className="text-3xl font-bold text-gray-900 mb-2">99.98%</div>
            <div className="text-sm text-gray-600">Last 7 Days</div>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div>
        <h2 className="text-lg font-bold text-gray-900 mb-4">Recent Activity</h2>
        <div className="bg-white rounded-xl shadow-lg border border-gray-200/50">
          <div className="p-8">
            <div className="text-center text-gray-500">
              <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
              </div>
              <p className="text-base font-semibold text-gray-700 mb-2">No recent activity</p>
              <p className="text-sm text-gray-500">Activity will appear here as you use the system</p>
            </div>
          </div>
        </div>
      </div>
    </PageWrapper>
  );
};

export default DashboardContent;
