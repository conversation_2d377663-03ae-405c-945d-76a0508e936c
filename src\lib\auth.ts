// src/lib/auth.ts
'use client';

export interface User {
  name: string;
  email: string;
  role: string;
  isAuthenticated: boolean;
}

export const getAuthState = (): User => {
  // Always return unauthenticated state to force fresh login
  return { name: '', email: '', role: '', isAuthenticated: false };
};

export const logout = async () => {
  if (typeof window !== 'undefined') {
    // Sign out from Supabase
    const { signOut } = await import('./supabase-auth-functions');
    await signOut();

    // Redirect to product page
    window.location.href = '/product';
  }
};

export const requireAuth = async () => {
  if (typeof window !== 'undefined') {
    // Always verify with Supabase session - no localStorage fallback
    try {
      const { getCurrentUser } = await import('./supabase-auth-functions');
      const currentUser = await getCurrentUser();
      if (!currentUser) {
        window.location.href = '/product';
        return false;
      }
      return true;
    } catch (error) {
      console.error('Auth verification failed:', error);
      window.location.href = '/product';
      return false;
    }
  }
  return false;
};

// New function to initialize auth state from Supabase
export const initializeAuth = async (): Promise<User | null> => {
  if (typeof window === 'undefined') {
    return null;
  }

  try {
    const { getCurrentUser } = await import('./supabase-auth-functions');
    const currentUser = await getCurrentUser();
    return currentUser;
  } catch (error) {
    console.error('Failed to initialize auth:', error);
    return null;
  }
};
