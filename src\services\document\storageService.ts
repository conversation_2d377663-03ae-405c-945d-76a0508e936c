// src/services/document/storageService.ts
import { SupabaseClient } from '@supabase/supabase-js';
import { DATABASE_CONFIG } from '../../constants/database';
import { Database } from '../../types/database';
import { BaseService } from '../core/baseService';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ErrorHandler, ServiceError } from '../core/errorHandler';

/**
 * File upload progress callback
 */
export type UploadProgressCallback = (progress: {
  loaded: number;
  total: number;
  percentage: number;
}) => void;

/**
 * File upload options
 */
export interface UploadOptions {
  bucket?: string;
  folder?: string;
  fileName?: string;
  maxSize?: number;
  allowedTypes?: string[];
  onProgress?: UploadProgressCallback;
  metadata?: Record<string, any>;
}

/**
 * Upload result
 */
export interface UploadResult {
  path: string;
  url: string;
  size: number;
  type: string;
  metadata?: Record<string, any>;
}

/**
 * Storage service for handling file uploads and management
 */
export class StorageService extends BaseService {
  private readonly defaultBucket: string;
  private readonly maxFileSize: number;
  private readonly allowedTypes: string[];

  constructor(client: SupabaseClient<Database>) {
    super(client);
    this.defaultBucket = DATABASE_CONFIG.STORAGE_BUCKET;
    this.maxFileSize = DATABASE_CONFIG.MAX_FILE_SIZE;
    this.allowedTypes = [
      'image/jpeg',
      'image/png',
      'image/gif',
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain'
    ];
  }

  /**
   * Upload a single file
   */
  async uploadFile(
    file: File,
    options: UploadOptions = {}
  ): Promise<UploadResult> {
    try {
      // Validate file
      this.validateFile(file, options);

      const bucket = options.bucket || this.defaultBucket;
      const folder = options.folder || 'uploads';
      const fileName = options.fileName || this.generateFileName(file);
      const filePath = `${folder}/${fileName}`;

      this.log('info', 'Starting file upload', {
        fileName: file.name,
        size: file.size,
        type: file.type,
        path: filePath
      });

      // Upload file to Supabase Storage
      const { data, error } = await this.client.storage
        .from(bucket)
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: false,
          metadata: options.metadata
        });

      if (error) {
        throw ErrorHandler.handle(error, 'File upload');
      }

      // Get public URL
      const { data: urlData } = this.client.storage
        .from(bucket)
        .getPublicUrl(filePath);

      const result: UploadResult = {
        path: data.path,
        url: urlData.publicUrl,
        size: file.size,
        type: file.type,
        metadata: options.metadata
      };

      this.log('info', 'File upload completed', result);
      return result;

    } catch (error) {
      this.log('error', 'File upload failed', { error, fileName: file.name });
      throw ErrorHandler.handle(error, 'Upload file');
    }
  }

  /**
   * Upload multiple files
   */
  async uploadFiles(
    files: File[],
    options: UploadOptions = {}
  ): Promise<UploadResult[]> {
    const results: UploadResult[] = [];
    const errors: { file: string; error: ServiceError }[] = [];

    for (const file of files) {
      try {
        const result = await this.uploadFile(file, {
          ...options,
          fileName: options.fileName ? 
            `${options.fileName}_${Date.now()}_${file.name}` : 
            undefined
        });
        results.push(result);
      } catch (error) {
        const serviceError = ErrorHandler.handle(error, `Upload file ${file.name}`);
        errors.push({ file: file.name, error: serviceError });
      }
    }

    if (errors.length > 0) {
      this.log('warn', 'Some files failed to upload', { errors });
      
      // If all files failed, throw error
      if (errors.length === files.length) {
        throw new ServiceError(
          ErrorCode.FILE_UPLOAD_ERROR,
          `All ${files.length} files failed to upload`,
          errors
        );
      }
    }

    return results;
  }

  /**
   * Delete a file
   */
  async deleteFile(
    filePath: string,
    bucket?: string
  ): Promise<void> {
    try {
      const targetBucket = bucket || this.defaultBucket;

      const { error } = await this.client.storage
        .from(targetBucket)
        .remove([filePath]);

      if (error) {
        throw ErrorHandler.handle(error, 'Delete file');
      }

      this.log('info', 'File deleted', { path: filePath, bucket: targetBucket });
    } catch (error) {
      this.log('error', 'File deletion failed', { error, path: filePath });
      throw ErrorHandler.handle(error, 'Delete file');
    }
  }

  /**
   * Delete multiple files
   */
  async deleteFiles(
    filePaths: string[],
    bucket?: string
  ): Promise<void> {
    try {
      const targetBucket = bucket || this.defaultBucket;

      const { error } = await this.client.storage
        .from(targetBucket)
        .remove(filePaths);

      if (error) {
        throw ErrorHandler.handle(error, 'Delete files');
      }

      this.log('info', 'Files deleted', { paths: filePaths, bucket: targetBucket });
    } catch (error) {
      this.log('error', 'Files deletion failed', { error, paths: filePaths });
      throw ErrorHandler.handle(error, 'Delete files');
    }
  }

  /**
   * Get file URL
   */
  async getFileUrl(
    filePath: string,
    bucket?: string,
    expiresIn?: number
  ): Promise<string> {
    try {
      const targetBucket = bucket || this.defaultBucket;

      if (expiresIn) {
        // Get signed URL for private files
        const { data, error } = await this.client.storage
          .from(targetBucket)
          .createSignedUrl(filePath, expiresIn);

        if (error) {
          throw ErrorHandler.handle(error, 'Get signed URL');
        }

        return data.signedUrl;
      } else {
        // Get public URL
        const { data } = this.client.storage
          .from(targetBucket)
          .getPublicUrl(filePath);

        return data.publicUrl;
      }
    } catch (error) {
      throw ErrorHandler.handle(error, 'Get file URL');
    }
  }

  /**
   * Check if file exists
   */
  async fileExists(
    filePath: string,
    bucket?: string
  ): Promise<boolean> {
    try {
      const targetBucket = bucket || this.defaultBucket;

      const { data, error } = await this.client.storage
        .from(targetBucket)
        .list(filePath.split('/').slice(0, -1).join('/'), {
          search: filePath.split('/').pop()
        });

      if (error) {
        return false;
      }

      return data.length > 0;
    } catch {
      return false;
    }
  }

  /**
   * Get file metadata
   */
  async getFileMetadata(
    filePath: string,
    bucket?: string
  ): Promise<any> {
    try {
      const targetBucket = bucket || this.defaultBucket;

      const { data, error } = await this.client.storage
        .from(targetBucket)
        .list(filePath.split('/').slice(0, -1).join('/'), {
          search: filePath.split('/').pop()
        });

      if (error) {
        throw ErrorHandler.handle(error, 'Get file metadata');
      }

      const file = data.find(f => f.name === filePath.split('/').pop());
      if (!file) {
        throw new ServiceError(ErrorCode.NOT_FOUND, 'File not found');
      }

      return file;
    } catch (error) {
      throw ErrorHandler.handle(error, 'Get file metadata');
    }
  }

  /**
   * Validate file before upload
   */
  private validateFile(file: File, options: UploadOptions): void {
    const maxSize = options.maxSize || this.maxFileSize;
    const allowedTypes = options.allowedTypes || this.allowedTypes;

    // Check file size
    if (file.size > maxSize) {
      throw new ServiceError(
        ErrorCode.FILE_TOO_LARGE,
        `File size ${file.size} exceeds maximum allowed size ${maxSize}`,
        null,
        'File validation',
        `File is too large. Maximum size allowed is ${this.formatFileSize(maxSize)}.`
      );
    }

    // Check file type
    if (!allowedTypes.includes(file.type)) {
      throw new ServiceError(
        ErrorCode.INVALID_FILE_TYPE,
        `File type ${file.type} is not allowed`,
        null,
        'File validation',
        `File type not supported. Allowed types: ${allowedTypes.join(', ')}.`
      );
    }

    // Check file name
    if (!file.name || file.name.trim() === '') {
      throw new ServiceError(
        ErrorCode.VALIDATION_ERROR,
        'File name is required',
        null,
        'File validation'
      );
    }
  }

  /**
   * Generate unique file name
   */
  private generateFileName(file: File): string {
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 15);
    const extension = file.name.split('.').pop();
    const baseName = file.name.split('.').slice(0, -1).join('.');
    
    return `${baseName}_${timestamp}_${randomString}.${extension}`;
  }

  /**
   * Format file size for display
   */
  private formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Clean up orphaned files (files not referenced in database)
   */
  async cleanupOrphanedFiles(
    bucket?: string,
    olderThanDays: number = 7
  ): Promise<string[]> {
    try {
      const targetBucket = bucket || this.defaultBucket;
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

      // This would require implementing logic to check which files
      // are referenced in the database vs. what exists in storage
      // For now, return empty array
      this.log('info', 'Cleanup orphaned files requested', {
        bucket: targetBucket,
        olderThanDays
      });

      return [];
    } catch (error) {
      throw ErrorHandler.handle(error, 'Cleanup orphaned files');
    }
  }
}
