// src/scripts/test-connection.ts
/**
 * Simple script to test Supabase connection and manually insert master data
 */

import { createClient } from '@supabase/supabase-js';
import { config } from 'dotenv';
import { resolve } from 'path';

// Load .env file from project root
config({ path: resolve(process.cwd(), '.env') });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testConnection() {
  console.log('🔍 Testing Supabase connection...');
  
  try {
    // Test basic connection by trying to select from classes table
    const { data, error } = await supabase
      .from('classes')
      .select('id')
      .limit(1);

    if (error) {
      console.error('❌ Connection test failed:', error);
      return false;
    }

    console.log('✅ Connection successful!');
    console.log(`📊 Classes table accessible, found ${data?.length || 0} records`);
    return true;
  } catch (error) {
    console.error('❌ Connection test failed:', error);
    return false;
  }
}

async function insertMasterData() {
  console.log('🌱 Inserting master data...');
  
  try {
    // Insert classes
    console.log('📚 Inserting classes...');
    const classesData = [
      { name: 'Nursery', description: 'Nursery Class for ages 3-4', is_active: true },
      { name: 'LKG', description: 'Lower Kindergarten for ages 4-5', is_active: true },
      { name: 'UKG', description: 'Upper Kindergarten for ages 5-6', is_active: true },
      { name: 'Class 1', description: 'First Standard for ages 6-7', is_active: true },
      { name: 'Class 2', description: 'Second Standard for ages 7-8', is_active: true },
      { name: 'Class 3', description: 'Third Standard for ages 8-9', is_active: true },
      { name: 'Class 4', description: 'Fourth Standard for ages 9-10', is_active: true },
      { name: 'Class 5', description: 'Fifth Standard for ages 10-11', is_active: true },
      { name: 'Class 6', description: 'Sixth Standard for ages 11-12', is_active: true },
      { name: 'Class 7', description: 'Seventh Standard for ages 12-13', is_active: true },
      { name: 'Class 8', description: 'Eighth Standard for ages 13-14', is_active: true },
      { name: 'Class 9', description: 'Ninth Standard for ages 14-15', is_active: true },
      { name: 'Class 10', description: 'Tenth Standard for ages 15-16', is_active: true }
    ];

    const { data: classes, error: classError } = await supabase
      .from('classes')
      .insert(classesData)
      .select();

    if (classError) {
      console.error('❌ Error inserting classes:', classError);
      return;
    }

    console.log(`✅ Inserted ${classes?.length || 0} classes`);

    // Insert guardian relations
    console.log('👨‍👩‍👧‍👦 Inserting guardian relations...');
    const guardianRelationsData = [
      { name: 'Father', is_active: true },
      { name: 'Mother', is_active: true },
      { name: 'Guardian', is_active: true },
      { name: 'Grandfather', is_active: true },
      { name: 'Grandmother', is_active: true },
      { name: 'Uncle', is_active: true },
      { name: 'Aunt', is_active: true },
      { name: 'Other', is_active: true }
    ];

    const { data: relations, error: relationError } = await supabase
      .from('guardian_relations')
      .insert(guardianRelationsData)
      .select();

    if (relationError) {
      console.error('❌ Error inserting guardian relations:', relationError);
      return;
    }

    console.log(`✅ Inserted ${relations?.length || 0} guardian relations`);

    // Insert academic years
    console.log('📅 Inserting academic years...');
    const currentYear = new Date().getFullYear();
    const academicYearsData = [
      {
        year: `${currentYear - 1}-${currentYear}`,
        start_date: `${currentYear - 1}-04-01`,
        end_date: `${currentYear}-03-31`,
        is_current: false,
        is_active: true
      },
      {
        year: `${currentYear}-${currentYear + 1}`,
        start_date: `${currentYear}-04-01`,
        end_date: `${currentYear + 1}-03-31`,
        is_current: true,
        is_active: true
      },
      {
        year: `${currentYear + 1}-${currentYear + 2}`,
        start_date: `${currentYear + 1}-04-01`,
        end_date: `${currentYear + 2}-03-31`,
        is_current: false,
        is_active: true
      }
    ];

    const { data: years, error: yearError } = await supabase
      .from('academic_years')
      .insert(academicYearsData)
      .select();

    if (yearError) {
      console.error('❌ Error inserting academic years:', yearError);
      return;
    }

    console.log(`✅ Inserted ${years?.length || 0} academic years`);

    // Insert sections for each class
    if (classes && classes.length > 0) {
      console.log('📝 Inserting sections...');
      const sectionsData = [];
      const sectionNames = ['A', 'B', 'C', 'D'];

      for (const cls of classes) {
        for (const sectionName of sectionNames) {
          sectionsData.push({
            name: sectionName,
            class_id: cls.id,
            max_capacity: 30,
            is_active: true
          });
        }
      }

      const { data: sections, error: sectionError } = await supabase
        .from('sections')
        .insert(sectionsData)
        .select();

      if (sectionError) {
        console.error('❌ Error inserting sections:', sectionError);
        return;
      }

      console.log(`✅ Inserted ${sections?.length || 0} sections`);
    }

    console.log('🎉 Master data insertion completed successfully!');
    
  } catch (error) {
    console.error('💥 Master data insertion failed:', error);
  }
}

async function main() {
  const connected = await testConnection();
  if (connected) {
    await insertMasterData();
  }
}

if (require.main === module) {
  main();
}
