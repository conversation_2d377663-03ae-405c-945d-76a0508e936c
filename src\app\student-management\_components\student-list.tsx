// src/components/student-management/student-list.tsx
'use client';

import { useState } from 'react';

interface Student {
  id: string;
  name: string;
  class: string;
  section: string;
  feesStatus: 'Paid' | 'Pending';
  extracurricular: string;
}

interface StudentListProps {
  onAddNewStudent: () => void;
}

const StudentList = ({ onAddNewStudent }: StudentListProps) => {
  const [classFilter, setClassFilter] = useState('All Classes');
  const [sectionFilter, setSectionFilter] = useState('All Sections');
  const [extracurricularFilter, setExtracurricularFilter] = useState('Any');
  const [feesStatusFilter, setFeesStatusFilter] = useState('Any');
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');

  // Student data - will be populated from actual service
  const [students] = useState<Student[]>([]);

  // Filter students based on selected criteria
  const filteredStudents = students.filter(student => 
    (classFilter === 'All Classes' || student.class === classFilter) &&
    (sectionFilter === 'All Sections' || student.section === sectionFilter) &&
    (extracurricularFilter === 'Any Activity' || student.extracurricular === extracurricularFilter) &&
    (feesStatusFilter === 'Any Status' || student.feesStatus === feesStatusFilter) &&
    (searchTerm === '' || student.name.toLowerCase().includes(searchTerm.toLowerCase()) || student.id.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // Sort students
  const sortedStudents = [...filteredStudents].sort((a, b) => {
    let aValue = a[sortBy as keyof Student];
    let bValue = b[sortBy as keyof Student];
    
    if (typeof aValue === 'string') aValue = aValue.toLowerCase();
    if (typeof bValue === 'string') bValue = bValue.toLowerCase();
    
    if (sortOrder === 'asc') {
      return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
    } else {
      return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
    }
  });

  const resetAllFilters = () => {
    setClassFilter('All Classes');
    setSectionFilter('All Sections');
    setExtracurricularFilter('Any Activity');
    setFeesStatusFilter('Any Status');
    setSearchTerm('');
  };

  const toggleSort = (field: string) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('asc');
    }
  };

  return (
    <div className="space-y-3">
      {/* Page Header */}
      <div className="flex items-center justify-end">
        <div className="flex items-center space-x-3">
          <button
            onClick={onAddNewStudent}
            className="btn-primary flex items-center space-x-2"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4" />
            </svg>
            <span>Add Student</span>
          </button>
          <button className="btn-outline flex items-center space-x-2">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <span>Export</span>
          </button>
        </div>
      </div>

      {/* Search and Filters Section with Gradient */}
      <div className="card-lg relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-indigo-50 via-white to-purple-50"></div>
        <div className="relative">
          <div className="flex items-center justify-between gap-4">
            {/* Search Bar */}
            <div className="relative flex-1 max-w-sm">
              <input
                type="text"
                placeholder="Search students..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="form-input-lg w-full pl-10 pr-4 bg-white/80 backdrop-blur-sm shadow-sm hover:shadow-md"
              />
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              {searchTerm && (
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                  <button
                    onClick={() => setSearchTerm('')}
                    className="text-gray-400 hover:text-gray-600 transition-colors"
                  >
                    <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  </button>
                </div>
              )}
            </div>

            {/* Quick Filters */}
            <div className="flex items-center gap-3">
              <div className="flex items-center">
                <button className="p-3 text-gray-500 hover:text-indigo-600 hover:bg-white/60 rounded-xl transition-all duration-200 backdrop-blur-sm" title="Filter by Class">
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2.5" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                  </svg>
                </button>
                <select
                  value={classFilter}
                  onChange={(e) => setClassFilter(e.target.value)}
                  className="form-select-md ml-2 bg-white/80 backdrop-blur-sm shadow-sm hover:shadow-md"
                >
                  <option>All Classes</option>
                  <option>Class 1</option>
                  <option>Class 2</option>
                  <option>Class 3</option>
                  <option>Class 4</option>
                  <option>Class 5</option>
                  <option>Class 6</option>
                  <option>Class 7</option>
                  <option>Class 8</option>
                </select>
              </div>

              <div className="flex items-center">
                <button className="p-3 text-gray-500 hover:text-indigo-600 hover:bg-white/60 rounded-xl transition-all duration-200 backdrop-blur-sm" title="Filter by Section">
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2.5" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 515.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                </button>
                <select
                  value={sectionFilter}
                  onChange={(e) => setSectionFilter(e.target.value)}
                  className="form-select-md ml-2 bg-white/80 backdrop-blur-sm shadow-sm hover:shadow-md"
                >
                  <option>All Sections</option>
                  <option>Section A</option>
                  <option>Section B</option>
                  <option>Section C</option>
                </select>
              </div>

              <div className="flex items-center">
                <button className="p-3 text-gray-500 hover:text-indigo-600 hover:bg-white/60 rounded-xl transition-all duration-200 backdrop-blur-sm" title="Filter by Activities">
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2.5" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                  </svg>
                </button>
                <select
                  value={extracurricularFilter}
                  onChange={(e) => setExtracurricularFilter(e.target.value)}
                  className="form-select-md ml-2 bg-white/80 backdrop-blur-sm shadow-sm hover:shadow-md"
                >
                  <option>Any Activity</option>
                  <option>Arts Club</option>
                  <option>Football Team</option>
                  <option>Music Band</option>
                  <option>Debate Club</option>
                  <option>Science Club</option>
                </select>
              </div>

              <div className="flex items-center">
                <button className="p-3 text-gray-500 hover:text-indigo-600 hover:bg-white/60 rounded-xl transition-all duration-200 backdrop-blur-sm" title="Filter by Fees Status">
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2.5" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </button>
                <select
                  value={feesStatusFilter}
                  onChange={(e) => setFeesStatusFilter(e.target.value)}
                  className="form-select-md ml-2 bg-white/80 backdrop-blur-sm shadow-sm hover:shadow-md"
                >
                  <option>Any Status</option>
                  <option>Paid</option>
                  <option>Pending</option>
                </select>
              </div>

              {/* Reset Button */}
              <button
                onClick={resetAllFilters}
                className="btn-outline bg-white/80 backdrop-blur-sm shadow-sm hover:shadow-md flex items-center space-x-2"
                title="Reset all filters"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                <span>Reset</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Results Section */}
      <div className="table-container">
        {/* Students Table */}
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="table-header">
              <tr className="border-b border-gray-700">
                <th className="table-header-cell text-gray-200">
                  <div className="flex items-center space-x-2">
                    <input type="checkbox" className="rounded border-gray-600 bg-gray-700 text-indigo-500 focus:ring-indigo-500" />
                    <button
                      onClick={() => toggleSort('name')}
                      className="flex items-center space-x-1 hover:text-white transition-colors"
                    >
                      <span>Student</span>
                      <div className="flex flex-col">
                        <svg className={`w-3 h-3 ${sortBy === 'name' && sortOrder === 'asc' ? 'text-indigo-400' : 'text-gray-500'}`} fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clipRule="evenodd" />
                        </svg>
                        <svg className={`w-3 h-3 -mt-1 ${sortBy === 'name' && sortOrder === 'desc' ? 'text-indigo-400' : 'text-gray-500'}`} fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                        </svg>
                      </div>
                    </button>
                  </div>
                </th>
                <th className="table-header-cell text-gray-200">
                  <button
                    onClick={() => toggleSort('id')}
                    className="flex items-center space-x-1 hover:text-white transition-colors"
                  >
                    <span>Student ID</span>
                    <div className="flex flex-col">
                      <svg className={`w-3 h-3 ${sortBy === 'id' && sortOrder === 'asc' ? 'text-indigo-400' : 'text-gray-500'}`} fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clipRule="evenodd" />
                      </svg>
                      <svg className={`w-3 h-3 -mt-1 ${sortBy === 'id' && sortOrder === 'desc' ? 'text-indigo-400' : 'text-gray-500'}`} fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                      </svg>
                    </div>
                  </button>
                </th>
                <th className="table-header-cell text-gray-200">
                  <button
                    onClick={() => toggleSort('class')}
                    className="flex items-center space-x-1 hover:text-white transition-colors"
                  >
                    <span>Class & Section</span>
                    <div className="flex flex-col">
                      <svg className={`w-3 h-3 ${sortBy === 'class' && sortOrder === 'asc' ? 'text-indigo-400' : 'text-gray-500'}`} fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clipRule="evenodd" />
                      </svg>
                      <svg className={`w-3 h-3 -mt-1 ${sortBy === 'class' && sortOrder === 'desc' ? 'text-indigo-400' : 'text-gray-500'}`} fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                      </svg>
                    </div>
                  </button>
                </th>
                <th className="table-header-cell text-gray-200">
                  <button
                    onClick={() => toggleSort('feesStatus')}
                    className="flex items-center space-x-1 hover:text-white transition-colors"
                  >
                    <span>Fees Status</span>
                    <div className="flex flex-col">
                      <svg className={`w-3 h-3 ${sortBy === 'feesStatus' && sortOrder === 'asc' ? 'text-indigo-400' : 'text-gray-500'}`} fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clipRule="evenodd" />
                      </svg>
                      <svg className={`w-3 h-3 -mt-1 ${sortBy === 'feesStatus' && sortOrder === 'desc' ? 'text-indigo-400' : 'text-gray-500'}`} fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                      </svg>
                    </div>
                  </button>
                </th>
                <th className="table-header-cell text-gray-200">
                  <button
                    onClick={() => toggleSort('extracurricular')}
                    className="flex items-center space-x-1 hover:text-white transition-colors"
                  >
                    <span>Activities</span>
                    <div className="flex flex-col">
                      <svg className={`w-3 h-3 ${sortBy === 'extracurricular' && sortOrder === 'asc' ? 'text-indigo-400' : 'text-gray-500'}`} fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clipRule="evenodd" />
                      </svg>
                      <svg className={`w-3 h-3 -mt-1 ${sortBy === 'extracurricular' && sortOrder === 'desc' ? 'text-indigo-400' : 'text-gray-500'}`} fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                      </svg>
                    </div>
                  </button>
                </th>
                <th className="table-header-cell text-center text-gray-200">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {sortedStudents.map((student) => (
                <tr key={student.id} className="table-row">
                  <td className="table-cell">
                    <div className="flex items-center space-x-3">
                      <input type="checkbox" className="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500" />
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold text-base-app">
                          {student.name.split(' ').map(n => n[0]).join('')}
                        </div>
                        <div>
                          <div className="font-semibold text-gray-900 text-base-app">{student.name}</div>
                          <div className="text-sm-app text-gray-500">ID: {student.id}</div>
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="table-cell">
                    <span className="font-mono text-base-app text-gray-600 bg-gray-100 px-2 py-1 rounded">
                      {student.id}
                    </span>
                  </td>
                  <td className="table-cell">
                    <div className="space-y-1">
                      <div className="text-base-app font-medium text-gray-900">{student.class}</div>
                      <div className="text-sm-app text-gray-500">{student.section}</div>
                    </div>
                  </td>
                  <td className="table-cell">
                    <span className={`inline-flex items-center px-2 py-0.5 rounded-md text-sm-app font-medium ${
                      student.feesStatus === 'Paid' 
                        ? 'bg-emerald-50 text-emerald-700 border border-emerald-200' 
                        : 'bg-red-50 text-red-700 border border-red-200'
                    }`}>
                      <div className={`w-1 h-1 rounded-full mr-1 ${
                        student.feesStatus === 'Paid' ? 'bg-emerald-500' : 'bg-red-500'
                      }`}></div>
                      {student.feesStatus}
                    </span>
                  </td>
                  <td className="table-cell">
                    <span className="inline-flex items-center px-2 py-0.5 rounded-md text-sm-app font-medium bg-blue-50 text-blue-700 border border-blue-200">
                      {student.extracurricular}
                    </span>
                  </td>
                  <td className="table-cell">
                    <div className="flex items-center justify-center space-x-1">
                      <button 
                        className="p-1.5 text-gray-500 hover:text-indigo-600 hover:bg-indigo-50 rounded transition-colors duration-150"
                        title="View Details"
                      >
                        <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                      </button>
                      <button 
                        className="p-1.5 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded transition-colors duration-150"
                        title="Edit Student"
                      >
                        <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                      </button>
                      <button 
                        className="p-1.5 text-gray-500 hover:text-red-600 hover:bg-red-50 rounded transition-colors duration-150"
                        title="Remove Student"
                      >
                        <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          {/* Empty State */}
          {sortedStudents.length === 0 && (
            <div className="text-center py-12 bg-white">
              <svg className="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
              </svg>
              <h3 className="section-header-sm">No students found</h3>
              <p className="card-content mb-4">
                {students.length === 0
                  ? "No students have been added yet. Start by adding your first student."
                  : "Try adjusting your search criteria or filters to find students."
                }
              </p>
              <button
                onClick={onAddNewStudent}
                className="btn-primary"
              >
                {students.length === 0 ? "Add First Student" : "Add New Student"}
              </button>
            </div>
          )}
        </div>

        {/* Enhanced Pagination */}
        {sortedStudents.length > 0 && (
          <div className="px-6 py-3 border-t border-gray-200 bg-gray-50">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <p className="text-base-app text-gray-700">
                  Showing <span className="font-semibold">1</span> to <span className="font-semibold">{sortedStudents.length}</span> of{' '}
                  <span className="font-semibold">{students.length}</span> students
                  {sortedStudents.length !== students.length && (
                    <span className="text-gray-500"> (filtered)</span>
                  )}
                </p>
                <div className="flex items-center space-x-1.5">
                  <label className="text-base-app text-gray-700">Show:</label>
                  <select className="form-select-sm">
                    <option>10</option>
                    <option>25</option>
                    <option>50</option>
                    <option>100</option>
                  </select>
                  <span className="text-base-app text-gray-700">per page</span>
                </div>
              </div>
              
              <nav className="flex items-center space-x-1">
                <button className="btn-outline btn-sm">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
                  </svg>
                </button>
                <button className="btn-primary btn-sm">
                  1
                </button>
                <button className="btn-outline btn-sm">
                  2
                </button>
                <button className="btn-outline btn-sm">
                  3
                </button>
                <button className="btn-outline btn-sm">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                  </svg>
                </button>
              </nav>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default StudentList;
