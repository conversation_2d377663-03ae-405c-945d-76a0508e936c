// src/services/documentManagementService.ts
import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '../types/database';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>r<PERSON><PERSON><PERSON>, ServiceError } from './core/errorHandler';

// Repository imports
import { DocumentRepository, IDocumentRepository } from '../repositories/documentRepository';
import { StorageService } from './document/storageService';

// Type imports
import {
    DATABASE_CONFIG,
    DocumentEntity,
    DocumentInsert
} from '../constants/database';
import { PaginatedResult, PaginationOptions } from '../repositories/baseRepository';

/**
 * Document upload interface
 */
export interface DocumentUpload {
  file: File;
  type: string;
  isRequired: boolean;
  studentId: string;
}

/**
 * Document upload result interface
 */
export interface DocumentUploadResult {
  success: boolean;
  document?: DocumentEntity;
  error?: string;
  uploadProgress?: number;
}

/**
 * Document validation result interface
 */
export interface DocumentValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * Document statistics interface
 */
export interface DocumentStatistics {
  total: number;
  approved: number;
  pending: number;
  rejected: number;
  byType: Record<string, number>;
  totalSize: number;
}

/**
 * Document management service interface
 */
export interface IDocumentManagementService {
  // Upload and storage
  uploadDocument(upload: DocumentUpload, onProgress?: (progress: number) => void): Promise<DocumentUploadResult>;
  uploadMultipleDocuments(uploads: DocumentUpload[], onProgress?: (progress: number) => void): Promise<DocumentUploadResult[]>;
  
  // Document management
  getStudentDocuments(studentId: string): Promise<DocumentEntity[]>;
  getDocumentById(id: string): Promise<DocumentEntity | null>;
  updateDocumentStatus(id: string, status: string, notes?: string): Promise<DocumentEntity>;
  deleteDocument(id: string): Promise<void>;
  
  // Document validation
  validateDocument(file: File, type: string): Promise<DocumentValidationResult>;
  validateDocumentRequirements(studentId: string): Promise<{ isComplete: boolean; missing: string[]; optional: string[] }>;
  
  // Document retrieval
  getDocumentUrl(id: string): Promise<string>;
  downloadDocument(id: string): Promise<Blob>;
  
  // Document organization
  getDocumentsByType(studentId: string, type: string): Promise<DocumentEntity[]>;
  getDocumentsByStatus(status: string, pagination?: PaginationOptions): Promise<PaginatedResult<DocumentEntity>>;
  
  // Statistics and reporting
  getDocumentStatistics(studentId?: string): Promise<DocumentStatistics>;
  getSystemDocumentStatistics(): Promise<DocumentStatistics>;
  
  // Maintenance
  cleanupOrphanedDocuments(olderThanDays?: number): Promise<string[]>;
  optimizeStorage(): Promise<{ savedSpace: number; optimizedCount: number }>;
}

/**
 * Document management service implementation
 * Handles document upload, validation, storage, and management
 */
export class DocumentManagementService implements IDocumentManagementService {
  private documentRepository: IDocumentRepository;
  private storageService: StorageService;

  constructor(client: SupabaseClient<Database>) {
    this.documentRepository = new DocumentRepository(client);
    this.storageService = new StorageService(client);
  }

  /**
   * Upload a single document
   * @param upload - Document upload data
   * @param onProgress - Progress callback
   * @returns Upload result
   */
  async uploadDocument(
    upload: DocumentUpload,
    _onProgress?: (progress: number) => void
  ): Promise<DocumentUploadResult> {
    try {
      console.log('Uploading document', {
        studentId: upload.studentId,
        type: upload.type,
        fileName: upload.file.name,
        fileSize: upload.file.size
      });

      // Validate document
      const validation = await this.validateDocument(upload.file, upload.type);
      if (!validation.isValid) {
        return {
          success: false,
          error: validation.errors.join(', ')
        };
      }

      // Generate file path
      const fileName = this.generateFileName(upload.studentId, upload.type, upload.file.name);
      const filePath = `students/${upload.studentId}/documents/${fileName}`;

      // Upload file to storage (simplified for now)
      try {
        const fileUrl = await this.storageService.getFileUrl(filePath);

        // Create document record
        const documentData: DocumentInsert = {
          student_id: upload.studentId,
          type: upload.type,
          file_name: fileName,
          original_name: upload.file.name,
          file_path: filePath,
          file_url: fileUrl,
          file_size: upload.file.size,
          mime_type: upload.file.type,
          uploaded_at: new Date().toISOString(),
          is_required: upload.isRequired,
          status: DATABASE_CONFIG.DOCUMENT_STATUS.PENDING,
          is_active: true
        };

        const document = await this.documentRepository.create(documentData);

        console.log('Document uploaded successfully', {
          documentId: document.id,
          studentId: upload.studentId,
          type: upload.type
        });

        return {
          success: true,
          document,
          uploadProgress: 100
        };
      } catch (uploadError) {
        return {
          success: false,
          error: uploadError instanceof Error ? uploadError.message : 'File upload failed'
        };
      }
    } catch (error) {
      console.error('Error uploading document', { error, upload });
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Upload multiple documents
   * @param uploads - Array of document uploads
   * @param onProgress - Progress callback
   * @returns Array of upload results
   */
  async uploadMultipleDocuments(
    uploads: DocumentUpload[],
    onProgress?: (progress: number) => void
  ): Promise<DocumentUploadResult[]> {
    try {
      console.log('Uploading multiple documents', { count: uploads.length });

      const results: DocumentUploadResult[] = [];
      let completedCount = 0;

      for (const upload of uploads) {
        const result = await this.uploadDocument(upload, (fileProgress) => {
          const overallProgress = ((completedCount + fileProgress / 100) / uploads.length) * 100;
          onProgress?.(overallProgress);
        });

        results.push(result);
        completedCount++;
        
        onProgress?.((completedCount / uploads.length) * 100);
      }

      console.log('Multiple documents upload completed', { 
        total: uploads.length,
        successful: results.filter(r => r.success).length 
      });

      return results;
    } catch (error) {
      console.error('Error uploading multiple documents', { error, uploads });
      throw ErrorHandler.handle(error, 'Upload multiple documents');
    }
  }

  /**
   * Get all documents for a student
   * @param studentId - Student ID
   * @returns Array of document entities
   */
  async getStudentDocuments(studentId: string): Promise<DocumentEntity[]> {
    try {
      console.log('Getting student documents', { studentId });

      const documents = await this.documentRepository.findByStudentId(studentId);

      console.log('Student documents retrieved', { 
        studentId, 
        count: documents.length 
      });

      return documents;
    } catch (error) {
      console.error('Error getting student documents', { error, studentId });
      throw ErrorHandler.handle(error, 'Get student documents');
    }
  }

  /**
   * Get document by ID
   * @param id - Document ID
   * @returns Document entity or null
   */
  async getDocumentById(id: string): Promise<DocumentEntity | null> {
    try {
      console.log('Getting document by ID', { documentId: id });

      const document = await this.documentRepository.findById(id);

      console.log('Document retrieved by ID', { documentId: id, found: !!document });
      return document;
    } catch (error) {
      console.error('Error getting document by ID', { error, documentId: id });
      throw ErrorHandler.handle(error, 'Get document by ID');
    }
  }

  /**
   * Update document status
   * @param id - Document ID
   * @param status - New status
   * @param notes - Optional notes
   * @returns Updated document entity
   */
  async updateDocumentStatus(id: string, status: string, notes?: string): Promise<DocumentEntity> {
    try {
      console.log('Updating document status', { documentId: id, status, notes });

      const document = await this.documentRepository.updateStatus(id, status, notes);

      console.log('Document status updated', { documentId: id, status });
      return document;
    } catch (error) {
      console.error('Error updating document status', { error, documentId: id, status });
      throw ErrorHandler.handle(error, 'Update document status');
    }
  }

  /**
   * Delete document
   * @param id - Document ID
   */
  async deleteDocument(id: string): Promise<void> {
    try {
      console.log('Deleting document', { documentId: id });

      const document = await this.documentRepository.findById(id);
      if (!document) {
        throw new ServiceError(
          ErrorCode.NOT_FOUND,
          'Document not found',
          null,
          'Delete document'
        );
      }

      // Delete file from storage
      await this.storageService.deleteFile(document.file_path);

      // Delete document record
      await this.documentRepository.softDelete(id);

      console.log('Document deleted successfully', { documentId: id });
    } catch (error) {
      console.error('Error deleting document', { error, documentId: id });
      throw ErrorHandler.handle(error, 'Delete document');
    }
  }

  /**
   * Validate document file
   * @param file - File to validate
   * @param type - Document type
   * @returns Validation result
   */
  async validateDocument(file: File, type: string): Promise<DocumentValidationResult> {
    try {
      const errors: string[] = [];
      const warnings: string[] = [];

      // File size validation
      if (file.size > DATABASE_CONFIG.MAX_FILE_SIZE) {
        errors.push(`File size ${this.formatFileSize(file.size)} exceeds maximum allowed size ${this.formatFileSize(DATABASE_CONFIG.MAX_FILE_SIZE)}`);
      }

      // File type validation
      if (!DATABASE_CONFIG.ALLOWED_FILE_TYPES.includes(file.type as any)) {
        errors.push(`File type ${file.type} is not allowed. Allowed types: ${DATABASE_CONFIG.ALLOWED_FILE_TYPES.join(', ')}`);
      }

      // File name validation
      if (file.name.length > 255) {
        errors.push('File name is too long (maximum 255 characters)');
      }

      // Document type validation
      const allDocumentTypes = [...DATABASE_CONFIG.REQUIRED_DOCUMENTS, ...DATABASE_CONFIG.OPTIONAL_DOCUMENTS];
      if (!allDocumentTypes.includes(type as any)) {
        errors.push(`Invalid document type: ${type}`);
      }

      // File content warnings
      if (file.size < 1024) {
        warnings.push('File size is very small, please ensure the document is complete');
      }

      return {
        isValid: errors.length === 0,
        errors,
        warnings
      };
    } catch (error) {
      console.error('Error validating document', { error, fileName: file.name, type });
      throw ErrorHandler.handle(error, 'Validate document');
    }
  }

  /**
   * Validate document requirements for a student
   * @param studentId - Student ID
   * @returns Validation result
   */
  async validateDocumentRequirements(studentId: string): Promise<{
    isComplete: boolean;
    missing: string[];
    optional: string[];
  }> {
    try {
      console.log('Validating document requirements', { studentId });

      const documents = await this.documentRepository.findByStudentId(studentId);
      const uploadedTypes = documents.map(doc => doc.type);

      const requiredTypes = DATABASE_CONFIG.REQUIRED_DOCUMENTS;
      const optionalTypes = DATABASE_CONFIG.OPTIONAL_DOCUMENTS;

      const missing = requiredTypes.filter(type => !uploadedTypes.includes(type));
      const optionalMissing = optionalTypes.filter(type => !uploadedTypes.includes(type));

      const result = {
        isComplete: missing.length === 0,
        missing,
        optional: optionalMissing
      };

      console.log('Document requirements validated', { 
        studentId, 
        isComplete: result.isComplete,
        missingCount: missing.length 
      });

      return result;
    } catch (error) {
      console.error('Error validating document requirements', { error, studentId });
      throw ErrorHandler.handle(error, 'Validate document requirements');
    }
  }

  /**
   * Get document download URL
   * @param id - Document ID
   * @returns Document URL
   */
  async getDocumentUrl(id: string): Promise<string> {
    try {
      console.log('Getting document URL', { documentId: id });

      const document = await this.documentRepository.findById(id);
      if (!document) {
        throw new ServiceError(
          ErrorCode.NOT_FOUND,
          'Document not found',
          null,
          'Get document URL'
        );
      }

      const url = await this.storageService.getFileUrl(document.file_path);

      console.log('Document URL retrieved', { documentId: id });
      return url;
    } catch (error) {
      console.error('Error getting document URL', { error, documentId: id });
      throw ErrorHandler.handle(error, 'Get document URL');
    }
  }

  /**
   * Download document as blob
   * @param id - Document ID
   * @returns Document blob
   */
  async downloadDocument(id: string): Promise<Blob> {
    try {
      console.log('Downloading document', { documentId: id });

      const document = await this.documentRepository.findById(id);
      if (!document) {
        throw new ServiceError(
          ErrorCode.NOT_FOUND,
          'Document not found',
          null,
          'Download document'
        );
      }

      // For now, return a placeholder blob
      // In a real implementation, you would fetch the file from storage
      const blob = new Blob(['Document content placeholder'], { type: document.mime_type });

      console.log('Document downloaded', { documentId: id, size: blob.size });
      return blob;
    } catch (error) {
      console.error('Error downloading document', { error, documentId: id });
      throw ErrorHandler.handle(error, 'Download document');
    }
  }

  /**
   * Get documents by type for a student
   * @param studentId - Student ID
   * @param type - Document type
   * @returns Array of document entities
   */
  async getDocumentsByType(studentId: string, type: string): Promise<DocumentEntity[]> {
    try {
      console.log('Getting documents by type', { studentId, type });

      const documents = await this.documentRepository.findByType(studentId, type);

      console.log('Documents by type retrieved', { 
        studentId, 
        type, 
        count: documents.length 
      });

      return documents;
    } catch (error) {
      console.error('Error getting documents by type', { error, studentId, type });
      throw ErrorHandler.handle(error, 'Get documents by type');
    }
  }

  /**
   * Get documents by status with pagination
   * @param status - Document status
   * @param pagination - Pagination options
   * @returns Paginated document results
   */
  async getDocumentsByStatus(
    status: string,
    pagination?: PaginationOptions
  ): Promise<PaginatedResult<DocumentEntity>> {
    try {
      console.log('Getting documents by status', { status });

      const result = await this.documentRepository.findByStatus(status, pagination);

      console.log('Documents by status retrieved', { 
        status, 
        count: result.data.length 
      });

      return result;
    } catch (error) {
      console.error('Error getting documents by status', { error, status });
      throw ErrorHandler.handle(error, 'Get documents by status');
    }
  }

  /**
   * Get document statistics for a student
   * @param studentId - Student ID (optional)
   * @returns Document statistics
   */
  async getDocumentStatistics(studentId?: string): Promise<DocumentStatistics> {
    try {
      console.log('Getting document statistics', { studentId });

      let documents: DocumentEntity[];
      
      if (studentId) {
        documents = await this.documentRepository.findByStudentId(studentId);
      } else {
        const result = await this.documentRepository.findAll();
        documents = result.data;
      }

      const stats = this.calculateDocumentStatistics(documents);

      console.log('Document statistics calculated', { 
        studentId, 
        total: stats.total 
      });

      return stats;
    } catch (error) {
      console.error('Error getting document statistics', { error, studentId });
      throw ErrorHandler.handle(error, 'Get document statistics');
    }
  }

  /**
   * Get system-wide document statistics
   * @returns System document statistics
   */
  async getSystemDocumentStatistics(): Promise<DocumentStatistics> {
    try {
      console.log('Getting system document statistics');

      return await this.getDocumentStatistics();
    } catch (error) {
      console.error('Error getting system document statistics', { error });
      throw ErrorHandler.handle(error, 'Get system document statistics');
    }
  }

  /**
   * Clean up orphaned documents
   * @param olderThanDays - Delete documents older than this many days
   * @returns Array of cleaned up document IDs
   */
  async cleanupOrphanedDocuments(olderThanDays: number = 30): Promise<string[]> {
    try {
      this.log('info', 'Cleaning up orphaned documents', { olderThanDays });

      const cleanedIds = await this.documentRepository.cleanupOrphanedDocuments(olderThanDays);

      this.log('info', 'Orphaned documents cleaned up', { count: cleanedIds.length });
      return cleanedIds;
    } catch (error) {
      this.log('error', 'Error cleaning up orphaned documents', { error, olderThanDays });
      throw ErrorHandler.handle(error, 'Cleanup orphaned documents');
    }
  }

  /**
   * Optimize storage by removing duplicate files
   * @returns Optimization result
   */
  async optimizeStorage(): Promise<{ savedSpace: number; optimizedCount: number }> {
    try {
      console.log('Optimizing storage');

      // This is a placeholder implementation
      // In a real system, you would implement deduplication logic
      
      const result = {
        savedSpace: 0,
        optimizedCount: 0
      };

      console.log('Storage optimization completed', result);
      return result;
    } catch (error) {
      console.error('Error optimizing storage', { error });
      throw ErrorHandler.handle(error, 'Optimize storage');
    }
  }

  /**
   * Private helper methods
   */

  private generateFileName(studentId: string, type: string, originalName: string): string {
    const timestamp = Date.now();
    const extension = originalName.split('.').pop();
    return `${studentId}_${type}_${timestamp}.${extension}`;
  }

  private formatFileSize(bytes: number): string {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  }

  private calculateDocumentStatistics(documents: DocumentEntity[]): DocumentStatistics {
    const stats: DocumentStatistics = {
      total: documents.length,
      approved: 0,
      pending: 0,
      rejected: 0,
      byType: {},
      totalSize: 0
    };

    for (const doc of documents) {
      // Count by status
      switch (doc.status) {
        case DATABASE_CONFIG.DOCUMENT_STATUS.APPROVED:
          stats.approved++;
          break;
        case DATABASE_CONFIG.DOCUMENT_STATUS.PENDING:
          stats.pending++;
          break;
        case DATABASE_CONFIG.DOCUMENT_STATUS.REJECTED:
          stats.rejected++;
          break;
      }

      // Count by type
      stats.byType[doc.type] = (stats.byType[doc.type] || 0) + 1;

      // Sum file sizes
      stats.totalSize += doc.file_size;
    }

    return stats;
  }
}
