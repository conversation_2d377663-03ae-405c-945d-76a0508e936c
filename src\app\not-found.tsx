'use client';

import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

export default function NotFoundPage() {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('');

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      router.push(`/dashboard?search=${encodeURIComponent(searchQuery.trim())}`);
    }
  };

  const quickLinks = [
    {
      title: 'Dashboard',
      description: 'Overview of your school management system',
      href: '/dashboard',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
        </svg>
      ),
      color: 'bg-blue-100 text-blue-600'
    },
    {
      title: 'Student Management',
      description: 'Manage student records, enrollment, and profiles',
      href: '/student-management',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
        </svg>
      ),
      color: 'bg-green-100 text-green-600'
    },
    {
      title: 'Academic Management',
      description: 'Manage classes, subjects, and academic records',
      href: '/academic-management',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
        </svg>
      ),
      color: 'bg-purple-100 text-purple-600'
    }
  ];

  return (
    <div className="h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50 flex items-center justify-center px-4 overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 left-1/4 w-72 h-72 bg-gradient-to-r from-indigo-400 to-purple-400 rounded-full mix-blend-multiply filter blur-xl opacity-20"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full mix-blend-multiply filter blur-xl opacity-20"></div>
      </div>

      <div className="relative z-10 max-w-4xl w-full">
        <div className="text-center">
          {/* EduPro Brand */}
          <div className="flex items-center justify-center mb-8">
            <Link href="/" className="flex items-center space-x-3 hover:opacity-80 transition-opacity">
              <div className="w-12 h-12 bg-indigo-600 rounded-xl flex items-center justify-center">
                <svg className="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                </svg>
              </div>
              <h1 className="text-2xl font-bold text-gray-900">EduPro</h1>
            </Link>
          </div>

          {/* 404 Visual */}
          <div className="mb-8">
            <div className="text-8xl font-bold bg-gradient-to-r from-indigo-200 to-purple-200 bg-clip-text text-transparent select-none mb-4">
              404
            </div>
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              <span className="bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
                Oops!
              </span>
              {' '}Page Not Found
            </h1>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto mb-8">
              This page seems to have taken a study break! Don't worry – EduPro has many other 
              <span className="font-semibold text-indigo-600"> powerful features</span> to help you manage your educational institution.
            </p>
          </div>

          {/* Search Box */}
          <div className="max-w-md mx-auto mb-8">
            <form onSubmit={handleSearch} className="relative">
              <input
                type="text"
                placeholder="Search for pages, features, or help..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full h-12 pl-12 pr-20 rounded-xl border-2 border-indigo-100 focus:border-indigo-500 focus:ring-4 focus:ring-indigo-50 transition-all duration-200 bg-white/80 backdrop-blur-sm"
              />
              <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <button
                type="submit"
                className="absolute inset-y-0 right-2 my-2 px-4 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-lg font-semibold hover:from-indigo-700 hover:to-purple-700 transition-all duration-200"
              >
                Search
              </button>
            </form>
          </div>

          {/* Quick Navigation */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
            {quickLinks.map((link) => (
              <Link
                key={link.href}
                href={link.href}
                className="group flex items-center p-4 rounded-xl bg-white/80 backdrop-blur-sm border border-white/50 hover:border-indigo-200 transition-all duration-200 transform hover:-translate-y-1 hover:shadow-lg"
              >
                <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${link.color} group-hover:scale-110 transition-transform duration-200`}>
                  {link.icon}
                </div>
                <div className="ml-3 text-left">
                  <h4 className="font-semibold text-gray-800 group-hover:text-indigo-600 transition-colors">
                    {link.title}
                  </h4>
                  <p className="text-sm text-gray-600 mt-1">
                    {link.description}
                  </p>
                </div>
                <svg className="w-5 h-5 text-gray-400 group-hover:text-indigo-600 group-hover:translate-x-1 transition-all duration-200 ml-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                </svg>
              </Link>
            ))}
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <Link 
              href="/dashboard"
              className="px-8 py-3 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-xl font-semibold hover:from-indigo-700 hover:to-purple-700 transition-all duration-200 transform hover:-translate-y-1 hover:shadow-xl"
            >
              Go to Dashboard
            </Link>
            <Link 
              href="/"
              className="px-8 py-3 border-2 border-indigo-200 text-indigo-600 rounded-xl font-semibold hover:border-indigo-500 hover:bg-indigo-50 transition-all duration-200 transform hover:-translate-y-1"
            >
              Return Home
            </Link>
            <button
              onClick={() => {
                const subject = encodeURIComponent('EduPro - Page Not Found Report');
                const body = encodeURIComponent(`I was looking for a page that doesn't exist.\n\nURL: ${window.location.href}\nTimestamp: ${new Date().toISOString()}\n\nAdditional details:`);
                window.open(`mailto:<EMAIL>?subject=${subject}&body=${body}`);
              }}
              className="px-8 py-3 bg-white border-2 border-gray-200 text-gray-600 rounded-xl font-semibold hover:border-gray-400 hover:bg-gray-50 transition-all duration-200 transform hover:-translate-y-1"
            >
              Contact Support
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
