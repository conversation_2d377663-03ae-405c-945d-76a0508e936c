// src/services/masterDataService.ts
import { DATABASE_TABLES } from '../config/database';
import { supabase } from '../lib/supabase';
import type { AcademicYear, Class, GuardianRelation, Section } from '../types/database';

export class MasterDataService {
  /**
   * Get all active classes
   */
  static async getClasses(): Promise<Class[]> {
    try {
      console.log('🔍 Fetching classes from Supabase...');

      const { data, error } = await supabase
        .from(DATABASE_TABLES.CLASSES)
        .select(`
          id,
          name,
          description,
          is_active,
          created_at,
          updated_at
        `)
        .eq('is_active', true)
        .order('name', { ascending: true });

      if (error) {
        console.error('❌ Error fetching classes from Supabase:', error);
        console.log('📋 Falling back to default classes data');
        return this.getDefaultClasses();
      }

      if (!data || data.length === 0) {
        console.warn('⚠️ No classes found in database, using default data');
        return this.getDefaultClasses();
      }

      console.log(`✅ Successfully fetched ${data.length} classes from Supabase`);
      return data;
    } catch (error) {
      console.error('❌ Failed to fetch classes from Supabase:', error);
      console.log('📋 Using default classes data as fallback');
      return this.getDefaultClasses();
    }
  }

  /**
   * Get default classes when table is empty
   */
  private static getDefaultClasses(): Class[] {
    return [
      { id: '1', name: 'Nursery', description: 'Nursery Class', is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
      { id: '2', name: 'LKG', description: 'Lower Kindergarten', is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
      { id: '3', name: 'UKG', description: 'Upper Kindergarten', is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
      { id: '4', name: 'Class 1', description: 'First Standard', is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
      { id: '5', name: 'Class 2', description: 'Second Standard', is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
      { id: '6', name: 'Class 3', description: 'Third Standard', is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
      { id: '7', name: 'Class 4', description: 'Fourth Standard', is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
      { id: '8', name: 'Class 5', description: 'Fifth Standard', is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
      { id: '9', name: 'Class 6', description: 'Sixth Standard', is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
      { id: '10', name: 'Class 7', description: 'Seventh Standard', is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
      { id: '11', name: 'Class 8', description: 'Eighth Standard', is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
      { id: '12', name: 'Class 9', description: 'Ninth Standard', is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
      { id: '13', name: 'Class 10', description: 'Tenth Standard', is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
      { id: '14', name: 'Class 11', description: 'Eleventh Standard', is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
      { id: '15', name: 'Class 12', description: 'Twelfth Standard', is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() }
    ];
  }
  /**
   * Get all active sections
   */
  static async getSections(): Promise<Section[]> {
    try {
      console.log('🔍 Fetching sections from Supabase...');

      const { data, error } = await supabase
        .from(DATABASE_TABLES.SECTIONS)
        .select(`
          id,
          name,
          class_id,
          max_capacity,
          is_active,
          created_at,
          updated_at
        `)
        .eq('is_active', true)
        .order('name', { ascending: true });

      if (error) {
        console.error('❌ Error fetching sections from Supabase:', error);
        console.log('📋 Falling back to default sections data');
        return this.getDefaultSections();
      }

      if (!data || data.length === 0) {
        console.warn('⚠️ No sections found in database, using default data');
        return this.getDefaultSections();
      }

      console.log(`✅ Successfully fetched ${data.length} sections from Supabase`);
      return data;
    } catch (error) {
      console.error('❌ Failed to fetch sections from Supabase:', error);
      console.log('📋 Using default sections data as fallback');
      return this.getDefaultSections();
    }
  }

  /**
   * Get default sections when table is empty
   */
  private static getDefaultSections(): Section[] {
    return [
      { id: '1', name: 'A', description: 'Section A', is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
      { id: '2', name: 'B', description: 'Section B', is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
      { id: '3', name: 'C', description: 'Section C', is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
      { id: '4', name: 'D', description: 'Section D', is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() }
    ];
  }
  /**
   * Get all active academic years
   */
  static async getAcademicYears(): Promise<AcademicYear[]> {
    try {
      console.log('🔍 Fetching academic years from Supabase...');

      const { data, error } = await supabase
        .from(DATABASE_TABLES.ACADEMIC_YEARS)
        .select(`
          id,
          year,
          start_date,
          end_date,
          is_current,
          is_active,
          created_at,
          updated_at
        `)
        .eq('is_active', true)
        .order('start_date', { ascending: false });

      if (error) {
        console.error('❌ Error fetching academic years from Supabase:', error);
        console.log('📋 Falling back to default academic years data');
        return this.getDefaultAcademicYears();
      }

      if (!data || data.length === 0) {
        console.warn('⚠️ No academic years found in database, using default data');
        return this.getDefaultAcademicYears();
      }

      console.log(`✅ Successfully fetched ${data.length} academic years from Supabase`);
      return data;
    } catch (error) {
      console.error('❌ Failed to fetch academic years from Supabase:', error);
      console.log('📋 Using default academic years data as fallback');
      return this.getDefaultAcademicYears();
    }
  }

  /**
   * Get default academic years when table is empty
   */
  private static getDefaultAcademicYears(): AcademicYear[] {
    const currentYear = new Date().getFullYear();
    return [
      {
        id: '1',
        year: `${currentYear}-${currentYear + 1}`,
        start_date: `${currentYear}-04-01`,
        end_date: `${currentYear + 1}-03-31`,
        is_current: true,
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: '2',
        year: `${currentYear - 1}-${currentYear}`,
        start_date: `${currentYear - 1}-04-01`,
        end_date: `${currentYear}-03-31`,
        is_current: false,
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: '3',
        year: `${currentYear + 1}-${currentYear + 2}`,
        start_date: `${currentYear + 1}-04-01`,
        end_date: `${currentYear + 2}-03-31`,
        is_current: false,
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ];
  }

  /**
   * Get current academic year
   */
  static async getCurrentAcademicYear(): Promise<AcademicYear | null> {
    try {
      console.log('🔍 Fetching current academic year from Supabase...');

      const { data, error } = await supabase
        .from(DATABASE_TABLES.ACADEMIC_YEARS)
        .select(`
          id,
          year,
          start_date,
          end_date,
          is_current,
          is_active,
          created_at,
          updated_at
        `)
        .eq('is_current', true)
        .eq('is_active', true)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          // No current academic year found, return default current year
          console.warn('⚠️ No current academic year found in database, using default');
          const defaultYears = this.getDefaultAcademicYears();
          return defaultYears.find(year => year.is_current) || defaultYears[0];
        }
        console.error('❌ Error fetching current academic year from Supabase:', error);
        console.log('📋 Falling back to default current academic year');
        const defaultYears = this.getDefaultAcademicYears();
        return defaultYears.find(year => year.is_current) || defaultYears[0];
      }

      console.log(`✅ Successfully fetched current academic year: ${data.year}`);
      return data;
    } catch (error) {
      console.error('❌ Failed to fetch current academic year from Supabase:', error);
      console.log('📋 Using default current academic year as fallback');
      const defaultYears = this.getDefaultAcademicYears();
      return defaultYears.find(year => year.is_current) || defaultYears[0];
    }
  }
  /**
   * Get all active guardian relations
   */
  static async getGuardianRelations(): Promise<GuardianRelation[]> {
    try {
      console.log('🔍 Fetching guardian relations from Supabase...');

      const { data, error } = await supabase
        .from(DATABASE_TABLES.GUARDIAN_RELATIONS)
        .select(`
          id,
          name,
          is_active,
          created_at,
          updated_at
        `)
        .eq('is_active', true)
        .order('name', { ascending: true });

      if (error) {
        // If table doesn't exist, return default guardian relations
        if (error.code === '42P01') {
          console.warn('⚠️ Guardian relations table does not exist, using default data');
          return this.getDefaultGuardianRelations();
        }
        console.error('❌ Error fetching guardian relations from Supabase:', error);
        console.log('📋 Falling back to default guardian relations data');
        return this.getDefaultGuardianRelations();
      }

      if (!data || data.length === 0) {
        console.warn('⚠️ No guardian relations found in database, using default data');
        return this.getDefaultGuardianRelations();
      }

      console.log(`✅ Successfully fetched ${data.length} guardian relations from Supabase`);
      return data;
    } catch (error) {
      console.error('❌ Failed to fetch guardian relations from Supabase:', error);
      console.log('📋 Using default guardian relations data as fallback');
      return this.getDefaultGuardianRelations();
    }
  }

  /**
   * Get default guardian relations when table doesn't exist
   */
  private static getDefaultGuardianRelations(): GuardianRelation[] {
    return [
      { id: '1', name: 'Father', description: 'Biological father', is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
      { id: '2', name: 'Mother', description: 'Biological mother', is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
      { id: '3', name: 'Guardian', description: 'Legal guardian', is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
      { id: '4', name: 'Uncle', description: 'Uncle (paternal or maternal)', is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
      { id: '5', name: 'Aunt', description: 'Aunt (paternal or maternal)', is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
      { id: '6', name: 'Grandfather', description: 'Grandfather (paternal or maternal)', is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
      { id: '7', name: 'Grandmother', description: 'Grandmother (paternal or maternal)', is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() }
    ];
  }

  /**
   * Get all master data in one call for forms
   */
  static async getAllMasterData() {
    try {
      console.log('🔄 Fetching all master data...');

      // Use Promise.allSettled to handle individual failures gracefully
      const results = await Promise.allSettled([
        this.getClasses(),
        this.getSections(),
        this.getAcademicYears(),
        this.getGuardianRelations(),
        this.getCurrentAcademicYear()
      ]);

      // Extract results or use fallback data
      const classes = results[0].status === 'fulfilled' ? results[0].value : this.getDefaultClasses();
      const sections = results[1].status === 'fulfilled' ? results[1].value : this.getDefaultSections();
      const academicYears = results[2].status === 'fulfilled' ? results[2].value : this.getDefaultAcademicYears();
      const guardianRelations = results[3].status === 'fulfilled' ? results[3].value : this.getDefaultGuardianRelations();
      const currentAcademicYear = results[4].status === 'fulfilled' ? results[4].value : this.getDefaultAcademicYears().find(y => y.is_current) || this.getDefaultAcademicYears()[0];

      // Log any failures
      results.forEach((result, index) => {
        const names = ['classes', 'sections', 'academicYears', 'guardianRelations', 'currentAcademicYear'];
        if (result.status === 'rejected') {
          console.warn(`⚠️  ${names[index]} failed, using fallback:`, result.reason);
        }
      });

      const masterData = {
        classes,
        sections,
        academicYears,
        guardianRelations,
        currentAcademicYear
      };

      console.log('✅ Master data loaded successfully:', {
        classes: masterData.classes.length,
        sections: masterData.sections.length,
        academicYears: masterData.academicYears.length,
        guardianRelations: masterData.guardianRelations.length,
        currentAcademicYear: masterData.currentAcademicYear?.year || 'None'
      });

      return masterData;
    } catch (error) {
      console.error('❌ Critical error fetching master data, using all fallback data:', error);

      // Return complete fallback data if everything fails
      const fallbackData = {
        classes: this.getDefaultClasses(),
        sections: this.getDefaultSections(),
        academicYears: this.getDefaultAcademicYears(),
        guardianRelations: this.getDefaultGuardianRelations(),
        currentAcademicYear: this.getDefaultAcademicYears().find(y => y.is_current) || this.getDefaultAcademicYears()[0]
      };

      console.log('🔄 Using complete fallback master data');
      return fallbackData;
    }
  }

  /**
   * Check if roll number exists in a class and section for an academic year
   */
  static async isRollNumberExists(
    rollNumber: string, 
    classId: string, 
    sectionId: string, 
    academicYearId: string,
    excludeStudentId?: string
  ): Promise<boolean> {
    let query = supabase
      .from(DATABASE_TABLES.STUDENTS)
      .select('id')
      .eq('roll_number', rollNumber)
      .eq('class_id', classId)
      .eq('section_id', sectionId)
      .eq('academic_year_id', academicYearId)
      .eq('is_active', true);

    if (excludeStudentId) {
      query = query.neq('id', excludeStudentId);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error checking roll number:', error);
      throw new Error(`Failed to check roll number: ${error.message}`);
    }

    return (data || []).length > 0;
  }
}
